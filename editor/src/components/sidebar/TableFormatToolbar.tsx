import { Table } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { v4 as uuidv4 } from "uuid";
import { PositionSizeSettings } from "@/components/sidebar/PositionSizeSettings";
import { TableCreateDialog } from "@/components/table/TableCreateDialog";
import type { Element } from "@/types/element";
import type { TableCell, TableProperties } from "@/types/table";
import type { ColorInfo } from "@/utils/apiService";
import { getScrollAwarePosition } from "@/utils/elementHelpers";
import {
	applyBordersToSelection,
	isCellSelectedInLogicalGrid,
	logicalPositionToCell,
} from "@/utils/tableUtils";
import { ptToPx } from "@/utils/unitConversion";
import { BorderSelectionControl } from "../ui/BorderSelectionControl";
import { Button } from "../ui/button";
import { ColorPicker } from "../ui/ColorPicker";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../ui/tabs";

interface TableFormatToolbarProps {
	onAddElement?: (element: Element) => void;
	selectedElement?: Element | null;
	onUpdateElement?: (element: Element) => void;
	// Add any other props needed for table formatting, e.g., editor-like state if cells are edited directly in sidebar
	apiColors: ColorInfo[];
	isLoadingColors: boolean;
	colorError: string | null;
}

export function TableFormatToolbar({
	onAddElement,
	selectedElement,
	onUpdateElement,
	apiColors,
	isLoadingColors,
	colorError,
}: TableFormatToolbarProps) {
	const [activeTab, setActiveTab] = useState<string>("layout");
	const [isTablePopoverOpen, setIsTablePopoverOpen] = useState(false);
	const prevSelectedTableIdRef = useRef<string | undefined | null>(null); // Keep track of the previously selected table ID

	// Ensure selectedElement is a table
	const currentTableElement =
		selectedElement?.type === "table" ? selectedElement : null;
	const tableProperties = currentTableElement?.tableProperties;

	// Local state to track the most recent table state for border operations
	// This ensures that subsequent border operations use the latest state, not stale props
	const [localTableState, setLocalTableState] = useState<Element | null>(null);

	// Update local state when selectedElement changes, but preserve border changes
	useEffect(() => {
		if (currentTableElement) {
			// Only reset local state if it's a different table (different ID) or if we don't have local state yet
			if (!localTableState || localTableState.id !== currentTableElement.id) {
				setLocalTableState(currentTableElement);
			} else {
				// Same table but potentially different selection - preserve our local border changes
				// but update the selection to reflect the current UI state
				setLocalTableState((prev) =>
					prev
						? {
								...prev,
								tableProperties: prev.tableProperties
									? {
											...prev.tableProperties,
											selection:
												currentTableElement.tableProperties?.selection || null,
										}
									: currentTableElement.tableProperties,
							}
						: currentTableElement,
				);
			}
		} else {
			setLocalTableState(null);
		}
	}, [currentTableElement, localTableState]);

	const handleCreateTable = (properties: TableProperties) => {
		if (onAddElement) {
			const { x, y } = getScrollAwarePosition();

			const newElement: Element = {
				id: uuidv4(),
				type: "table",
				x,
				y,
				width: properties.columnWidths?.reduce((a, b) => a + b, 0) || 0, // Sum of column widths
				height: properties.rowHeights?.reduce((a, b) => a + b, 0) || 0, // Sum of row heights
				rotation: 0,
				currentPageId: "", // This should be set based on the current page context
				tableProperties: properties,
			};
			onAddElement(newElement);
			setIsTablePopoverOpen(false);
		}
	};

	const handleUpdateTableProperties = (
		updatedProps: Partial<TableProperties>,
	) => {
		// Use localTableState if available, otherwise fall back to currentTableElement
		const elementToUpdate = localTableState || currentTableElement;

		if (elementToUpdate && onUpdateElement && elementToUpdate.tableProperties) {
			const newTableProperties = {
				...elementToUpdate.tableProperties,
				...updatedProps,
			};
			const updatedElement = {
				...elementToUpdate,
				tableProperties: newTableProperties,
			};

			// Update local state immediately to ensure subsequent operations use the latest state
			setLocalTableState(updatedElement);

			// Also update the parent component
			onUpdateElement(updatedElement);
		}
	};

	const handleUpdateElementLayout = (updatedElement: Element) => {
		if (onUpdateElement && currentTableElement) {
			onUpdateElement({
				...currentTableElement,
				...updatedElement, // Apply layout changes (x, y, width, height, rotation)
			});
		}
	};

	useEffect(() => {
		const currentTableId = currentTableElement?.id;
		const currentSelection = currentTableElement?.tableProperties?.selection;
		const hasMultiCellSelection =
			currentSelection &&
			(currentSelection.start.row !== currentSelection.end.row ||
				currentSelection.start.col !== currentSelection.end.col);

		if (currentTableId) {
			if (hasMultiCellSelection) {
				if (activeTab !== "properties") {
					setActiveTab("properties");
				}
			} else if (prevSelectedTableIdRef.current !== currentTableId) {
				setActiveTab("layout");
			}
			prevSelectedTableIdRef.current = currentTableId;
		} else {
			// If no table is selected, and a table was previously selected, clear the ref.
			// This ensures that if a table is deselected, and then a *new* table is selected,
			// it will correctly default to the layout tab.
			if (prevSelectedTableIdRef.current) {
				prevSelectedTableIdRef.current = null;
			}
			// Optionally, switch to a default tab or disable tabs if no element is selected.
			// For now, activeTab remains, which is fine if tabs are disabled.
		}
	}, [currentTableElement, activeTab]);

	const renderSpanControls = () => {
		// Determine if exactly one cell is selected in the current table
		const singleCellSelected =
			currentTableElement &&
			tableProperties?.selection &&
			tableProperties.selection.start.row ===
				tableProperties.selection.end.row &&
			tableProperties.selection.start.col === tableProperties.selection.end.col;

		// Default placeholder values when no table or no selection
		let currentColspan = 1;
		let currentRowspan = 1;
		let maxColspan = 1;
		let maxRowspan = 1;

		if (singleCellSelected && tableProperties && tableProperties.selection) {
			const { row, col } = tableProperties.selection.start;
			// Convert logical position to actual cell array indices
			const cellPosition = logicalPositionToCell(
				tableProperties.cells,
				row,
				col,
				tableProperties.rows,
				tableProperties.columns,
			);

			if (cellPosition) {
				const cell =
					tableProperties.cells[cellPosition.rowIndex][cellPosition.colIndex];
				if (cell) {
					currentColspan = cell.colspan;
					currentRowspan = cell.rowspan;
				}
			}
			maxColspan = tableProperties.columns - col;
			maxRowspan = tableProperties.rows - row;
		} else if (tableProperties) {
			// If table exists but no single selection, limit max to full table dimensions
			maxColspan = tableProperties.columns;
			maxRowspan = tableProperties.rows;
		}

		const onSpanChange = (
			newColspan: number | null,
			newRowspan: number | null,
		) => {
			if (!singleCellSelected || !tableProperties || !tableProperties.selection)
				return;

			const { row, col } = tableProperties.selection.start;

			// Convert logical position to actual cell array indices
			const cellPosition = logicalPositionToCell(
				tableProperties.cells,
				row,
				col,
				tableProperties.rows,
				tableProperties.columns,
			);

			if (!cellPosition) {
				console.warn(
					`Could not find cell at logical position [${row}][${col}]`,
				);
				return;
			}

			// Deep clone cells to avoid state mutation
			const newCells = JSON.parse(
				JSON.stringify(tableProperties.cells),
			) as TableCell[][];

			const targetCell = newCells[cellPosition.rowIndex][cellPosition.colIndex];
			if (!targetCell) {
				console.warn(
					`Target cell not found at [${cellPosition.rowIndex}][${cellPosition.colIndex}]`,
				);
				return;
			}

			/* ------------------------- COLSPAN HANDLING ------------------------- */
			if (newColspan !== null && newColspan !== targetCell.colspan) {
				const delta = newColspan - targetCell.colspan; // positive = increase, negative = decrease

				if (delta > 0) {
					// We need to consume columns to the right of the current cell.
					let remaining = delta;
					const cursor = cellPosition.colIndex + 1;

					while (
						remaining > 0 &&
						cursor < newCells[cellPosition.rowIndex].length
					) {
						const nextCell = newCells[cellPosition.rowIndex][cursor];
						if (nextCell.colspan <= remaining) {
							// Remove the whole cell block
							remaining -= nextCell.colspan;
							newCells[cellPosition.rowIndex].splice(cursor, 1);
							// Do NOT advance cursor – items shift left
						} else {
							// Shrink the next cell's colspan and we're done
							nextCell.colspan -= remaining;
							remaining = 0;
						}
					}
				} else if (delta < 0) {
					// We need to insert new standalone cells after the target cell.
					const cellsToInsert = -delta;

					const defaultCell: TableCell = {
						content: "",
						colspan: 1,
						rowspan: 1,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						backgroundColor: "transparent",
						verticalAlign: "top",
					};

					for (let i = 0; i < cellsToInsert; i++) {
						newCells[cellPosition.rowIndex].splice(
							cellPosition.colIndex + 1,
							0,
							{ ...defaultCell },
						);
					}
				}

				// Finally, update the target cell's colspan to the desired value
				targetCell.colspan = newColspan;
			}

			/* ------------------------- ROWSPAN HANDLING ------------------------- */
			if (newRowspan !== null && newRowspan !== targetCell.rowspan) {
				// Basic rowspan update – extending/shrinking vertically can get complex.
				// For now, simply update the rowspan; additional logic can be added later
				targetCell.rowspan = newRowspan;
			}

			handleUpdateTableProperties({ cells: newCells });
		};

		return (
			<div className="space-y-2" key="span-controls">
				<div className="text-sm font-medium">Zellenspanne</div>
				<div className="grid grid-cols-2 gap-2 items-center">
					<div>
						<Label htmlFor="colspan-input" className="text-xs">
							Colspan
						</Label>
						<Input
							id="colspan-input"
							type="number"
							min={1}
							max={maxColspan}
							value={currentColspan}
							disabled={!singleCellSelected}
							onChange={(e) => {
								let value = parseInt(e.target.value, 10);
								if (Number.isNaN(value) || value < 1) value = 1;
								if (value > maxColspan) value = maxColspan;
								onSpanChange(value, null);
							}}
						/>
					</div>
					<div>
						<Label htmlFor="rowspan-input" className="text-xs">
							Rowspan
						</Label>
						<Input
							id="rowspan-input"
							type="number"
							min={1}
							max={maxRowspan}
							value={currentRowspan}
							disabled={!singleCellSelected}
							onChange={(e) => {
								let value = parseInt(e.target.value, 10);
								if (Number.isNaN(value) || value < 1) value = 1;
								if (value > maxRowspan) value = maxRowspan;
								onSpanChange(null, value);
							}}
						/>
					</div>
				</div>
			</div>
		);
	};

	return (
		<div className="space-y-4 format-toolbar">
			{onAddElement && (
				<div className="space-y-2">
					<Popover
						open={isTablePopoverOpen}
						onOpenChange={setIsTablePopoverOpen}
					>
						<PopoverTrigger asChild>
							<Button
								variant="outline"
								className="w-full flex items-center justify-center gap-2"
							>
								<Table className="h-4 w-4" />
								Tabelle hinzufügen
							</Button>
						</PopoverTrigger>
						<PopoverContent className="w-auto p-0" align="start">
							<TableCreateDialog
								onCreateTable={handleCreateTable}
								onClose={() => setIsTablePopoverOpen(false)}
							/>
						</PopoverContent>
					</Popover>
				</div>
			)}

			<Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
				<TabsList className="grid w-full grid-cols-2">
					<TabsTrigger value="properties">Eigenschaften</TabsTrigger>
					<TabsTrigger value="layout">Position</TabsTrigger>
				</TabsList>

				<TabsContent value="properties" className="space-y-4 mt-2">
					{/* Always show span controls at the top */}
					{renderSpanControls()}

					{currentTableElement && tableProperties ? (
						<>
							<div className="space-y-2">
								<div className="text-sm font-medium">Rahmen</div>
								{/* Placeholder for border controls */}
								<p className="text-xs text-gray-500">
									Rahmenstil, -farbe, -stärke hier einfügen.
								</p>
								<div className="space-y-1"></div>
							</div>
							<div className="space-y-2">
								<div className="text-sm font-medium">
									Hintergrund (Ausgewählte Zellen)
								</div>
								<ColorPicker
									label=""
									currentColor={
										tableProperties.selectedCellsBackgroundColor || "#FFFFFF"
									}
									onColorChange={(newColor) => {
										handleUpdateTableProperties({
											selectedCellsBackgroundColor: newColor,
										});
									}}
									apiColors={apiColors}
									isLoadingColors={isLoadingColors}
									colorError={colorError}
								/>
							</div>
							{/* Border Selection UI for multi-selected cells */}
							{(() => {
								const selection = tableProperties.selection;

								if (selection) {
									// Use the most recent table state for the BorderSelectionControl
									// This ensures it analyzes the latest border state, not stale props
									const elementToUse = localTableState || currentTableElement;
									const currentTableProperties =
										elementToUse?.tableProperties || tableProperties;

									// Render if ANY cell(s) are selected
									return (
										<BorderSelectionControl
											tableProperties={currentTableProperties}
											onUpdateCellBorders={(
												currentSelection,
												activeLines,
												borderWidthPt,
												borderColorHex,
											) => {
												// Use the most recent table state (local state if available, otherwise props)
												// This ensures we start with the latest border changes, not stale props
												const elementToUse =
													localTableState || currentTableElement;
												const currentCells =
													elementToUse?.tableProperties?.cells || [];

												// First, create a version of cells that correctly reflects selectedCellsBackgroundColor
												// on top of any existing individual cell backgroundColors.
												const cellsWithUpdatedBackgrounds = currentCells.map(
													(row, rIdx) =>
														row.map((cell, cIdx) => {
															const newCellState = { ...cell }; // Start with a shallow copy of the current cell from props

															// If a global selection background color is set and this cell is part of the current selection, apply it.
															if (
																tableProperties.selectedCellsBackgroundColor &&
																currentSelection
															) {
																const isCellSelectedForBackground =
																	isCellSelectedInLogicalGrid(
																		currentCells,
																		rIdx,
																		cIdx,
																		currentSelection,
																	);

																if (isCellSelectedForBackground) {
																	newCellState.backgroundColor =
																		tableProperties.selectedCellsBackgroundColor;
																}
															}
															// newCellState now has its original backgroundColor, or tableProperties.selectedCellsBackgroundColor if it was selected.
															return newCellState;
														}),
												);

												// Deep copy these cells before applying border modifications.
												const newCells = JSON.parse(
													JSON.stringify(cellsWithUpdatedBackgrounds),
												) as TableCell[][];

												const newWidthPx =
													borderWidthPt !== null ? ptToPx(borderWidthPt) : null;
												const newColorHex = borderColorHex; // This can be null

												// Apply borders using the utility function
												const updatedCells = applyBordersToSelection(
													newCells,
													currentSelection,
													activeLines,
													newWidthPx,
													newColorHex,
													tableProperties.rows,
													tableProperties.columns,
													tableProperties.borderColor || "#000000",
												);

												handleUpdateTableProperties({ cells: updatedCells });
											}}
											selectedElementId={currentTableElement?.id}
											apiColors={apiColors}
											isLoadingColors={isLoadingColors}
											colorError={colorError}
										/>
									);
								}
								return null;
							})()}
						</>
					) : (
						<p className="text-sm text-gray-500">
							Wählen Sie eine Tabelle aus, um deren Eigenschaften zu bearbeiten.
						</p>
					)}
				</TabsContent>

				<TabsContent value="layout" className="space-y-4 mt-2">
					{currentTableElement ? (
						<PositionSizeSettings
							element={currentTableElement}
							onUpdate={handleUpdateElementLayout}
						/>
					) : (
						<p className="text-sm text-gray-500">
							Wählen Sie eine Tabelle aus, um deren Position und Größe zu
							bearbeiten.
						</p>
					)}
				</TabsContent>
			</Tabs>
		</div>
	);
}
