import { describe, expect, it } from "vitest";
import type { TableCell } from "@/types/table";
import { applyBordersToSelection, applyCellBorderSide } from "../tableUtils";

describe("Table Border Utilities", () => {
	const createTestCell = (colspan = 1, rowspan = 1): TableCell => ({
		content: "Test",
		colspan,
		rowspan,
		backgroundColor: null,
		borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
	});

	describe("applyCellBorderSide", () => {
		it("should apply border when line is active", () => {
			const cell = createTestCell();
			applyCellBorderSide(cell, "top", true, 2, "#ff0000");

			expect(cell.borderSettings?.top).toEqual({
				width: 2,
				color: "#ff0000",
			});
		});

		it("should not modify border when line is inactive", () => {
			const cell = createTestCell();
			cell.borderSettings = { top: { width: 3, color: "#00ff00" } };

			applyCellBorderSide(cell, "top", false, 2, "#ff0000");

			expect(cell.borderSettings?.top).toEqual({
				width: 3,
				color: "#00ff00",
			});
		});

		it("should remove border when width is 0", () => {
			const cell = createTestCell();
			cell.borderSettings = { top: { width: 3, color: "#00ff00" } };

			applyCellBorderSide(cell, "top", true, 0, null);

			expect(cell.borderSettings?.top).toBeUndefined();
			expect(cell.borderWidths.top).toBe(0);
		});

		it("should preserve existing color when only width is changed", () => {
			const cell = createTestCell();
			cell.borderSettings = { top: { width: 1, color: "#00ff00" } };

			applyCellBorderSide(cell, "top", true, 3, null);

			expect(cell.borderSettings?.top).toEqual({
				width: 3,
				color: "#00ff00",
			});
		});
	});

	describe("applyBordersToSelection", () => {
		it("should apply outer borders to single cell selection", () => {
			const cells = [[createTestCell()]];
			const selection = { start: { row: 0, col: 0 }, end: { row: 0, col: 0 } };
			const activeLines = {
				outerTop: true,
				outerBottom: true,
				outerLeft: true,
				outerRight: true,
				innerHorizontal: false,
				innerVertical: false,
			};

			const result = applyBordersToSelection(
				cells,
				selection,
				activeLines,
				2,
				"#ff0000",
				1,
				1,
			);

			const cell = result[0][0];
			expect(cell.borderSettings?.top?.width).toBe(2);
			expect(cell.borderSettings?.bottom?.width).toBe(2);
			expect(cell.borderSettings?.left?.width).toBe(2);
			expect(cell.borderSettings?.right?.width).toBe(2);
		});

		it("should handle rowspan cells correctly", () => {
			const cells = [
				[createTestCell(1, 2), createTestCell()],
				[createTestCell()], // This row has one less cell due to rowspan
			];
			const selection = { start: { row: 0, col: 0 }, end: { row: 1, col: 1 } };
			const activeLines = {
				outerTop: true,
				outerBottom: false,
				outerLeft: false,
				outerRight: false,
				innerHorizontal: false,
				innerVertical: true,
			};

			const result = applyBordersToSelection(
				cells,
				selection,
				activeLines,
				3,
				"#00ff00",
				2,
				2,
			);

			// The rowspan cell should have top border
			expect(result[0][0].borderSettings?.top?.width).toBe(3);
			// The rowspan cell should have right border (inner vertical)
			expect(result[0][0].borderSettings?.right?.width).toBe(3);
		});

		it("should handle colspan cells correctly", () => {
			const cells = [
				[createTestCell(2, 1)],
				[createTestCell(), createTestCell()],
			];
			const selection = { start: { row: 0, col: 0 }, end: { row: 1, col: 1 } };
			const activeLines = {
				outerTop: true,
				outerBottom: false,
				outerLeft: false,
				outerRight: false,
				innerHorizontal: true,
				innerVertical: false,
			};

			const result = applyBordersToSelection(
				cells,
				selection,
				activeLines,
				4,
				"#0000ff",
				2,
				2,
			);

			// The colspan cell should have top border
			expect(result[0][0].borderSettings?.top?.width).toBe(4);
			// The colspan cell should have bottom border (inner horizontal)
			expect(result[0][0].borderSettings?.bottom?.width).toBe(4);
		});

		it("should preserve existing borders when not overridden", () => {
			const cell1 = createTestCell();
			cell1.borderSettings = { left: { width: 5, color: "#purple" } };

			const cells = [[cell1, createTestCell()]];
			const selection = { start: { row: 0, col: 0 }, end: { row: 0, col: 1 } };
			const activeLines = {
				outerTop: true,
				outerBottom: false,
				outerLeft: false, // Not active, should preserve existing
				outerRight: false,
				innerHorizontal: false,
				innerVertical: false,
			};

			const result = applyBordersToSelection(
				cells,
				selection,
				activeLines,
				2,
				"#ff0000",
				1,
				2,
			);

			// Should preserve the existing left border
			expect(result[0][0].borderSettings?.left).toEqual({
				width: 5,
				color: "#purple",
			});
			// Should apply the new top border
			expect(result[0][0].borderSettings?.top?.width).toBe(2);
		});
	});
});
