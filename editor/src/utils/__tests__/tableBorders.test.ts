import { describe, expect, it } from "vitest";
import type { TableCell } from "@/types/table";
import { applyBordersToSelection, applyCellBorderSide } from "../tableUtils";

describe("Table Border Utilities", () => {
	const createTestCell = (colspan = 1, rowspan = 1): TableCell => ({
		content: "Test",
		colspan,
		rowspan,
		backgroundColor: null,
		borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
	});

	describe("applyCellBorderSide", () => {
		it("should apply border when line is active", () => {
			const cell = createTestCell();
			applyCellBorderSide(cell, "top", true, 2, "#ff0000");

			expect(cell.borderSettings?.top).toEqual({
				width: 2,
				color: "#ff0000",
			});
		});

		it("should not modify border when line is inactive", () => {
			const cell = createTestCell();
			cell.borderSettings = { top: { width: 3, color: "#00ff00" } };

			applyCellBorderSide(cell, "top", false, 2, "#ff0000");

			expect(cell.borderSettings?.top).toEqual({
				width: 3,
				color: "#00ff00",
			});
		});

		it("should remove border when width is 0", () => {
			const cell = createTestCell();
			cell.borderSettings = { top: { width: 3, color: "#00ff00" } };

			applyCellBorderSide(cell, "top", true, 0, null);

			expect(cell.borderSettings?.top).toBeUndefined();
			expect(cell.borderWidths.top).toBe(0);
		});

		it("should preserve existing color when only width is changed", () => {
			const cell = createTestCell();
			cell.borderSettings = { top: { width: 1, color: "#00ff00" } };

			applyCellBorderSide(cell, "top", true, 3, null);

			expect(cell.borderSettings?.top).toEqual({
				width: 3,
				color: "#00ff00",
			});
		});
	});

	describe("applyBordersToSelection", () => {
		it("should apply outer borders to single cell selection", () => {
			const cells = [[createTestCell()]];
			const selection = { start: { row: 0, col: 0 }, end: { row: 0, col: 0 } };
			const activeLines = {
				outerTop: true,
				outerBottom: true,
				outerLeft: true,
				outerRight: true,
				innerHorizontal: false,
				innerVertical: false,
			};

			const result = applyBordersToSelection(
				cells,
				selection,
				activeLines,
				2,
				"#ff0000",
				1,
				1,
			);

			const cell = result[0][0];
			expect(cell.borderSettings?.top?.width).toBe(2);
			expect(cell.borderSettings?.bottom?.width).toBe(2);
			expect(cell.borderSettings?.left?.width).toBe(2);
			expect(cell.borderSettings?.right?.width).toBe(2);
		});

		it("should preserve borders from previous selections when applying new borders", () => {
			// Create a 2x2 table
			const cells = [
				[createTestCell(), createTestCell()],
				[createTestCell(), createTestCell()],
			];

			// First selection: apply borders to top-left cell
			const firstSelection = {
				start: { row: 0, col: 0 },
				end: { row: 0, col: 0 },
			};
			const firstActiveLines = {
				outerTop: true,
				outerBottom: true,
				outerLeft: true,
				outerRight: true,
				innerHorizontal: false,
				innerVertical: false,
			};

			const cellsAfterFirst = applyBordersToSelection(
				cells,
				firstSelection,
				firstActiveLines,
				3,
				"#ff0000",
				2,
				2,
			);

			// Verify first selection applied correctly
			expect(cellsAfterFirst[0][0].borderSettings?.top?.width).toBe(3);
			expect(cellsAfterFirst[0][0].borderSettings?.bottom?.width).toBe(3);
			expect(cellsAfterFirst[0][0].borderSettings?.left?.width).toBe(3);
			expect(cellsAfterFirst[0][0].borderSettings?.right?.width).toBe(3);

			// Second selection: apply borders to bottom-right cell
			const secondSelection = {
				start: { row: 1, col: 1 },
				end: { row: 1, col: 1 },
			};
			const secondActiveLines = {
				outerTop: true,
				outerBottom: true,
				outerLeft: true,
				outerRight: true,
				innerHorizontal: false,
				innerVertical: false,
			};

			const cellsAfterSecond = applyBordersToSelection(
				cellsAfterFirst, // Use the result from the first operation
				secondSelection,
				secondActiveLines,
				2,
				"#00ff00",
				2,
				2,
			);

			// Verify first selection borders are still preserved
			expect(cellsAfterSecond[0][0].borderSettings?.top?.width).toBe(3);
			expect(cellsAfterSecond[0][0].borderSettings?.bottom?.width).toBe(3);
			expect(cellsAfterSecond[0][0].borderSettings?.left?.width).toBe(3);
			expect(cellsAfterSecond[0][0].borderSettings?.right?.width).toBe(3);
			expect(cellsAfterSecond[0][0].borderSettings?.top?.color).toBe("#ff0000");

			// Verify second selection borders are applied
			expect(cellsAfterSecond[1][1].borderSettings?.top?.width).toBe(2);
			expect(cellsAfterSecond[1][1].borderSettings?.bottom?.width).toBe(2);
			expect(cellsAfterSecond[1][1].borderSettings?.left?.width).toBe(2);
			expect(cellsAfterSecond[1][1].borderSettings?.right?.width).toBe(2);
			expect(cellsAfterSecond[1][1].borderSettings?.top?.color).toBe("#00ff00");
		});

		it("should simulate the bug scenario: multiple cell selections with border changes", () => {
			// Create a 3x3 table to simulate the bug scenario
			const cells = [
				[createTestCell(), createTestCell(), createTestCell()],
				[createTestCell(), createTestCell(), createTestCell()],
				[createTestCell(), createTestCell(), createTestCell()],
			];

			// Step 1: Select multiple cells (top row) and set their borders
			const firstSelection = {
				start: { row: 0, col: 0 },
				end: { row: 0, col: 2 },
			};
			const firstActiveLines = {
				outerTop: true,
				outerBottom: true,
				outerLeft: true,
				outerRight: true,
				innerHorizontal: false,
				innerVertical: true,
			};

			const cellsAfterFirstSelection = applyBordersToSelection(
				cells,
				firstSelection,
				firstActiveLines,
				3,
				"#ff0000",
				3,
				3,
			);

			// Verify first selection borders are applied
			expect(cellsAfterFirstSelection[0][0].borderSettings?.top?.width).toBe(3);
			expect(cellsAfterFirstSelection[0][1].borderSettings?.top?.width).toBe(3);
			expect(cellsAfterFirstSelection[0][2].borderSettings?.top?.width).toBe(3);

			// Step 2: Select other cells (bottom row) and set their borders
			const secondSelection = {
				start: { row: 2, col: 0 },
				end: { row: 2, col: 2 },
			};
			const secondActiveLines = {
				outerTop: true,
				outerBottom: true,
				outerLeft: true,
				outerRight: true,
				innerHorizontal: false,
				innerVertical: true,
			};

			const cellsAfterSecondSelection = applyBordersToSelection(
				cellsAfterFirstSelection, // Use the result from the first operation
				secondSelection,
				secondActiveLines,
				2,
				"#00ff00",
				3,
				3,
			);

			// Step 3: Verify that borders from step 1 are still preserved
			expect(cellsAfterSecondSelection[0][0].borderSettings?.top?.width).toBe(
				3,
			);
			expect(cellsAfterSecondSelection[0][0].borderSettings?.top?.color).toBe(
				"#ff0000",
			);
			expect(cellsAfterSecondSelection[0][1].borderSettings?.top?.width).toBe(
				3,
			);
			expect(cellsAfterSecondSelection[0][1].borderSettings?.top?.color).toBe(
				"#ff0000",
			);
			expect(cellsAfterSecondSelection[0][2].borderSettings?.top?.width).toBe(
				3,
			);
			expect(cellsAfterSecondSelection[0][2].borderSettings?.top?.color).toBe(
				"#ff0000",
			);

			// Step 4: Verify that borders from step 2 are applied
			expect(
				cellsAfterSecondSelection[2][0].borderSettings?.bottom?.width,
			).toBe(2);
			expect(
				cellsAfterSecondSelection[2][0].borderSettings?.bottom?.color,
			).toBe("#00ff00");
			expect(
				cellsAfterSecondSelection[2][1].borderSettings?.bottom?.width,
			).toBe(2);
			expect(
				cellsAfterSecondSelection[2][1].borderSettings?.bottom?.color,
			).toBe("#00ff00");
			expect(
				cellsAfterSecondSelection[2][2].borderSettings?.bottom?.width,
			).toBe(2);
			expect(
				cellsAfterSecondSelection[2][2].borderSettings?.bottom?.color,
			).toBe("#00ff00");
		});

		it("should simulate the exact bug scenario: borders disappearing when not using updated state", () => {
			// This test simulates what would happen with the old buggy behavior
			// where each border operation starts from the original cells instead of the updated state
			const originalCells = [
				[createTestCell(), createTestCell()],
				[createTestCell(), createTestCell()],
			];

			// First selection: apply borders to top row
			const firstSelection = {
				start: { row: 0, col: 0 },
				end: { row: 0, col: 1 },
			};
			const firstActiveLines = {
				outerTop: true,
				outerBottom: true,
				outerLeft: true,
				outerRight: true,
				innerHorizontal: false,
				innerVertical: true,
			};

			const cellsAfterFirst = applyBordersToSelection(
				originalCells,
				firstSelection,
				firstActiveLines,
				3,
				"#ff0000",
				2,
				2,
			);

			// Verify first borders are applied
			expect(cellsAfterFirst[0][0].borderSettings?.top?.width).toBe(3);
			expect(cellsAfterFirst[0][1].borderSettings?.top?.width).toBe(3);

			// Second selection: apply borders to bottom row
			// CORRECT behavior: use the result from the first operation
			const secondSelection = {
				start: { row: 1, col: 0 },
				end: { row: 1, col: 1 },
			};
			const secondActiveLines = {
				outerTop: true,
				outerBottom: true,
				outerLeft: true,
				outerRight: true,
				innerHorizontal: false,
				innerVertical: true,
			};

			const cellsAfterSecondCorrect = applyBordersToSelection(
				cellsAfterFirst, // Use updated state - CORRECT
				secondSelection,
				secondActiveLines,
				2,
				"#00ff00",
				2,
				2,
			);

			// BUGGY behavior: start from original cells again (simulating the old bug)
			const cellsAfterSecondBuggy = applyBordersToSelection(
				originalCells, // Use original state - BUGGY
				secondSelection,
				secondActiveLines,
				2,
				"#00ff00",
				2,
				2,
			);

			// With correct behavior: first borders should be preserved
			expect(cellsAfterSecondCorrect[0][0].borderSettings?.top?.width).toBe(3);
			expect(cellsAfterSecondCorrect[0][0].borderSettings?.top?.color).toBe(
				"#ff0000",
			);
			expect(cellsAfterSecondCorrect[0][1].borderSettings?.top?.width).toBe(3);
			expect(cellsAfterSecondCorrect[0][1].borderSettings?.top?.color).toBe(
				"#ff0000",
			);

			// With buggy behavior: first borders would be missing
			expect(cellsAfterSecondBuggy[0][0].borderSettings?.top).toBeUndefined();
			expect(cellsAfterSecondBuggy[0][1].borderSettings?.top).toBeUndefined();

			// Both should have the second borders applied
			expect(cellsAfterSecondCorrect[1][0].borderSettings?.bottom?.width).toBe(
				2,
			);
			expect(cellsAfterSecondBuggy[1][0].borderSettings?.bottom?.width).toBe(2);
		});

		it("should handle rowspan cells correctly", () => {
			const cells = [
				[createTestCell(1, 2), createTestCell()],
				[createTestCell()], // This row has one less cell due to rowspan
			];
			const selection = { start: { row: 0, col: 0 }, end: { row: 1, col: 1 } };
			const activeLines = {
				outerTop: true,
				outerBottom: false,
				outerLeft: false,
				outerRight: false,
				innerHorizontal: false,
				innerVertical: true,
			};

			const result = applyBordersToSelection(
				cells,
				selection,
				activeLines,
				3,
				"#00ff00",
				2,
				2,
			);

			// The rowspan cell should have top border
			expect(result[0][0].borderSettings?.top?.width).toBe(3);
			// The rowspan cell should have right border (inner vertical)
			expect(result[0][0].borderSettings?.right?.width).toBe(3);
		});

		it("should handle colspan cells correctly", () => {
			const cells = [
				[createTestCell(2, 1)],
				[createTestCell(), createTestCell()],
			];
			const selection = { start: { row: 0, col: 0 }, end: { row: 1, col: 1 } };
			const activeLines = {
				outerTop: true,
				outerBottom: false,
				outerLeft: false,
				outerRight: false,
				innerHorizontal: true,
				innerVertical: false,
			};

			const result = applyBordersToSelection(
				cells,
				selection,
				activeLines,
				4,
				"#0000ff",
				2,
				2,
			);

			// The colspan cell should have top border
			expect(result[0][0].borderSettings?.top?.width).toBe(4);
			// The colspan cell should have bottom border (inner horizontal)
			expect(result[0][0].borderSettings?.bottom?.width).toBe(4);
		});

		it("should preserve existing borders when not overridden", () => {
			const cell1 = createTestCell();
			cell1.borderSettings = { left: { width: 5, color: "#purple" } };

			const cells = [[cell1, createTestCell()]];
			const selection = { start: { row: 0, col: 0 }, end: { row: 0, col: 1 } };
			const activeLines = {
				outerTop: true,
				outerBottom: false,
				outerLeft: false, // Not active, should preserve existing
				outerRight: false,
				innerHorizontal: false,
				innerVertical: false,
			};

			const result = applyBordersToSelection(
				cells,
				selection,
				activeLines,
				2,
				"#ff0000",
				1,
				2,
			);

			// Should preserve the existing left border
			expect(result[0][0].borderSettings?.left).toEqual({
				width: 5,
				color: "#purple",
			});
			// Should apply the new top border
			expect(result[0][0].borderSettings?.top?.width).toBe(2);
		});
	});
});
